import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { addItemAsync, updateItem, deleteItem } from '../store';
import './ReduxDemo.css';

const ReduxDemo = () => {
  const items = useSelector(state => state.items);
  const dispatch = useDispatch();
  
  // Local state for form inputs
  const [newItemText, setNewItemText] = useState('');
  const [editingId, setEditingId] = useState(null);
  const [editText, setEditText] = useState('');

  // Redux Actions Demo
  const handleAddItem = () => {
    if (newItemText.trim()) {
      const newItem = {
        id: Date.now(),
        text: newItemText.trim()
      };
      dispatch(addItemAsync(newItem));
      setNewItemText('');
    }
  };

  const handleStartEdit = (item) => {
    setEditingId(item.id);
    setEditText(item.text);
  };

  const handleSaveEdit = () => {
    if (editText.trim()) {
      dispatch(updateItem({ id: editingId, text: editText.trim() }));
      setEditingId(null);
      setEditText('');
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditText('');
  };

  const handleDeleteItem = (id) => {
    dispatch(deleteItem(id));
  };

  const handleKeyPress = (e, action) => {
    if (e.key === 'Enter') {
      action();
    }
  };

  // Filter items by status for demonstration
  const activeItems = items.filter(item => item.status !== 'exiting');
  const totalItems = activeItems.length;

  return (
    <div className="redux-demo">
      <div className="demo-header">
        <h2>🔄 Redux Demo Component</h2>
        <p>This component demonstrates Redux state management patterns:</p>
        <ul className="demo-features">
          <li>✅ <strong>useSelector</strong> - Reading state from Redux store</li>
          <li>✅ <strong>useDispatch</strong> - Dispatching actions to update state</li>
          <li>✅ <strong>Redux Toolkit</strong> - Modern Redux with createSlice</li>
          <li>✅ <strong>Optimistic UI</strong> - Immediate feedback with animation states</li>
        </ul>
      </div>

      <div className="demo-stats">
        <div className="stat-card">
          <span className="stat-number">{totalItems}</span>
          <span className="stat-label">Total Items</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{items.filter(i => i.status === 'entering').length}</span>
          <span className="stat-label">Adding</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{items.filter(i => i.status === 'updating').length}</span>
          <span className="stat-label">Updating</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{items.filter(i => i.status === 'exiting').length}</span>
          <span className="stat-label">Removing</span>
        </div>
      </div>

      <div className="demo-actions">
        <div className="add-item-section">
          <h3>Add New Item</h3>
          <div className="input-group">
            <input
              type="text"
              value={newItemText}
              onChange={(e) => setNewItemText(e.target.value)}
              onKeyDown={(e) => handleKeyPress(e, handleAddItem)}
              placeholder="Enter item text..."
              className="demo-input"
            />
            <button 
              onClick={handleAddItem}
              disabled={!newItemText.trim()}
              className="demo-btn demo-btn-primary"
            >
              Add Item
            </button>
          </div>
        </div>

        <div className="items-section">
          <h3>Items ({totalItems})</h3>
          {activeItems.length === 0 ? (
            <div className="empty-state">
              <p>No items yet. Add one above to see Redux in action!</p>
            </div>
          ) : (
            <div className="items-list">
              {activeItems.map(item => (
                <div
                  key={item.id}
                  className={`item-card ${item.status ? 'item-' + item.status : ''}`}
                >
                  {editingId === item.id ? (
                    <div className="edit-mode">
                      <input
                        type="text"
                        value={editText}
                        onChange={(e) => setEditText(e.target.value)}
                        onKeyPress={(e) => handleKeyPress(e, handleSaveEdit)}
                        className="demo-input"
                        autoFocus
                      />
                      <div className="edit-actions">
                        <button 
                          onClick={handleSaveEdit}
                          className="demo-btn demo-btn-success"
                        >
                          Save
                        </button>
                        <button 
                          onClick={handleCancelEdit}
                          className="demo-btn demo-btn-secondary"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="view-mode">
                      <div className="item-content">
                        <span className="item-text">{item.text}</span>
                        <span className="item-status">
                          {item.status && `(${item.status})`}
                        </span>
                      </div>
                      <div className="item-actions">
                        <button 
                          onClick={() => handleStartEdit(item)}
                          className="demo-btn demo-btn-secondary"
                        >
                          Edit
                        </button>
                        <button 
                          onClick={() => handleDeleteItem(item.id)}
                          className="demo-btn demo-btn-danger"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="demo-footer">
        <h4>🧠 Redux Concepts Demonstrated:</h4>
        <div className="concepts-grid">
          <div className="concept">
            <strong>State Management:</strong> All item data is stored in Redux store
          </div>
          <div className="concept">
            <strong>Actions:</strong> addItem, updateItem, deleteItem dispatched to store
          </div>
          <div className="concept">
            <strong>Reducers:</strong> Pure functions that update state immutably
          </div>
          <div className="concept">
            <strong>Selectors:</strong> useSelector hook to read specific state slices
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReduxDemo;
