.redux-demo {
  max-width: 800px;
  margin: 20px auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  font-family: 'Inter', 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 2px solid #f0f2f5;
}

.demo-header h2 {
  color: #1a1a1a;
  margin-bottom: 12px;
  font-size: 2rem;
  font-weight: 700;
}

.demo-header p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 16px;
}

.demo-features {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin: 0;
}

.demo-features li {
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.95rem;
  text-align: left;
}

.demo-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.demo-actions {
  margin-bottom: 32px;
}

.add-item-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
}

.add-item-section h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
}

.input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.demo-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.demo-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.demo-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.demo-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.demo-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.demo-btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.demo-btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

.demo-btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(86, 171, 47, 0.4);
}

.demo-btn-secondary {
  background: #6c757d;
  color: white;
}

.demo-btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.demo-btn-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.demo-btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.items-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-style: italic;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.item-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
}

/* Animation states */
.item-entering {
  animation: slideInUp 0.4s ease-out;
  border-color: #56ab2f;
  background: #f0fff4;
}

.item-updating {
  animation: pulse 0.4s ease-in-out;
  border-color: #ffc107;
  background: #fffbf0;
}

.item-exiting {
  animation: slideOutDown 0.4s ease-in;
  border-color: #ff6b6b;
  background: #fff5f5;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideOutDown {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.view-mode {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-content {
  flex: 1;
}

.item-text {
  font-size: 1.1rem;
  color: #333;
  margin-right: 12px;
}

.item-status {
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
}

.item-actions {
  display: flex;
  gap: 8px;
}

.edit-mode {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.edit-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.demo-footer {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 2px solid #f0f2f5;
}

.demo-footer h4 {
  margin-bottom: 16px;
  color: #333;
}

.concepts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.concept {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.concept strong {
  color: #667eea;
}

/* Responsive design */
@media (max-width: 768px) {
  .redux-demo {
    margin: 10px;
    padding: 16px;
  }
  
  .input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .view-mode {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .item-actions {
    justify-content: center;
  }
  
  .demo-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
