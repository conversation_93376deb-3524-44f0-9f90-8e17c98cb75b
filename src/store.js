import { configureStore, createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Example async thunk to fetch data from remote API
export const addItemAsync = createAsyncThunk(
  'items/addItemAsync',
  async (payload) => {
    // Fetch a random todo item by generating a random id between 1 and 200
    const randomId = Math.floor(Math.random() * 200) + 1;
    const response = await fetch(`https://jsonplaceholder.typicode.com/todos/${randomId}`);
    const data = await response.json();
    // Merge fetched data with payload
    return {
      id: payload.id,
      text: data.title, // use fetched data
    };
  }
);

const itemsSlice = createSlice({
  name: 'items',
  initialState: [],
  // so only reducers is enough, no define action and the payload of what action should bring
  reducers: {
    addItem: (state, action) => {
      state.push({ id: action.payload.id, text: action.payload.text, status: 'entering' });
    },
    updateItem: (state, action) => {
      const item = state.find(i => i.id === action.payload.id);
      if (item) {
        item.text = action.payload.text;
        item.status = 'updating';
      }
    },
    deleteItem: (state, action) => {
      const item = state.find(i => i.id === action.payload);
      if (item) {
        item.status = 'exiting';
      }
    },
    setIdle: (state, action) => {
      const item = state.find(i => i.id === action.payload);
      if (item) {
        item.status = 'idle';
      }
    },
    removeItem: (state, action) => {
      return state.filter(i => i.id !== action.payload);
    }
  },
  extraReducers: (builder) => {
    builder.addCase(addItemAsync.fulfilled, (state, action) => {
      state.push({ id: action.payload.id, text: action.payload.text, status: 'entering' });
    });
  }
});


// really cool, so actions are created by createSlice
export const {
  addItem,
  updateItem,
  deleteItem,
  setIdle,
  removeItem
} = itemsSlice.actions;

const store = configureStore({
  reducer: {
    items: itemsSlice.reducer
  }
});

export default store;
