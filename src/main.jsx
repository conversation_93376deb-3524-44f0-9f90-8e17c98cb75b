import React from 'react'
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { Provider } from 'react-redux'
import App from './App.jsx'
import store from './store'
import './index.css'
import ReduxDemo from './components/ReduxDemo.jsx'

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <Provider store={store}>
      {/* <App /> */}
      <ReduxDemo></ReduxDemo>
    </Provider>
  </StrictMode>
);